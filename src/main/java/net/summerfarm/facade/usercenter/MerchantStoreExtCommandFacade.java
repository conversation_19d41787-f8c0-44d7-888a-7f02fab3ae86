package net.summerfarm.facade.usercenter;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreExtResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 用户中心模块-门店打印配送单查询<br/>
 * date: 2024/4/7 15:23<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class MerchantStoreExtCommandFacade {

    @DubboReference
    private MerchantStoreExtCommandProvider merchantStoreExtCommandProvider;

    /**
     * 新增配置属性
     *
     * @param summerFarmMIds
     * @return
     */
    public void addMerchantStoreExt(Long mid, String key, String value) {
        if (mid == null) {
            return;
        }
        MerchantStoreExtCommandReq req = new MerchantStoreExtCommandReq();
        req.setMId(mid);
        req.setProKey(key);
        req.setProValue(value);
        try {
            merchantStoreExtCommandProvider.addOrUpdateMerchantStoreExt(req);
        } catch (Exception e) {
            log.error("设置门店扩展属性error", e);
        }
    }


}
