package net.summerfarm.mapper.manage.repository;

import net.summerfarm.facade.auth.AuthUserAuthFacade;
import net.summerfarm.facade.auth.AuthUserQueryFacade;
import net.summerfarm.mapper.manage.AdminAuthExtendMapper;
import net.summerfarm.model.domain.AdminAuthExtend;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class AdminAuthExtendRepository {
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    AuthUserQueryFacade authUserQueryFacade;
    @Resource
    AuthUserAuthFacade authUserAuthFacade;

    /**
     * 根据userid查询绑定信息
     *
     * @param type
     * @param userId
     * @return
     */
    public AdminAuthExtend selectByUserId(Integer type, String userId) {
        //来源是admin user_id type
        return authUserAuthFacade.selectByUserId(type, userId);
    }

    /**
     * 根据userid查询绑定信息
     *
     * @param type
     * @param adminId
     * @return
     */
    public AdminAuthExtend selectByAdminId(Integer type, Integer adminId) {
        //来源是admin user_id type
        return authUserAuthFacade.selectByAdminId(type, adminId);

    }



    /**
     * 跟进仓库no获取具有该仓库权限的人员信息,只查找具有销售角色的用户
     *
     * @param type        0、钉钉 1、CRM小程序
     * @param warehouseNo 仓库no,注意权限表中该字段为varchar
     * @return
     */
    public List<AdminAuthExtend> selectAdminByWarehouseNo(Integer type, String warehouseNo) {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(5));
        if (CollectionUtils.isEmpty(baseUserIds)) {
            return new ArrayList<>();
        }
        List<Long> userBaseIds = adminAuthExtendMapper.selectAdminByWarehouseNo(warehouseNo, baseUserIds);
        if (CollectionUtils.isEmpty(userBaseIds)) {
            return new ArrayList<>();
        }
        return authUserAuthFacade.selectByBaseUserIds(userBaseIds, type);
    }
}
