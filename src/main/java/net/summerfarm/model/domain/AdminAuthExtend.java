package net.summerfarm.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2020/2/13  14:19
 */
@Data
public class AdminAuthExtend {

    /**
     * user_id
     */
    public final static String DEVELOPMENT_USER_ID = "214323351435511875";

    private Integer id;

    /**
     * 认证类型：0、钉钉 1、CRM小程序
     */
    private Integer type;

    /**
     *  状态 0 有效 1 无效
     */
    private int status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 后台id
     */
    private Integer adminId;

    /**
     * 钉钉昵称userId
     */
    private String userId;

    /**
     * openid
     */
    private String openid;

    /**
     * unionId
     */
    private String unionId;

}
